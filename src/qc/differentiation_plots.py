import os
import numpy as np
import matplotlib.pyplot as plt
import pandas as pd
from matplotlib.patches import Rectangle

# Cache directory for plots
cache = "static/img/cache/qc"


class DifferentiationPlateHeatmap:
    """Plate layout heatmap showing differentiation efficiency per well"""
    
    def __init__(self, df: pd.DataFrame, plate_name: str = None, day: int = None):
        self.df = df.copy()
        self.plate_name = plate_name or "Unknown"
        self.day = day or "Unknown"
        self.fn = f"diff_heatmap_{self.plate_name}_day{self.day}.png"
        
        # Calculate efficiency percentage
        self.df['efficiency'] = (self.df['positive'] / self.df['total'] * 100).fillna(0)
        
        self.title = f"Differentiation Efficiency Heatmap - {self.plate_name} Day {self.day}"
        self.caption = f"""
            Plate layout showing differentiation efficiency (% positive cells) for each well.
            Color scale represents percentage of cells with lipid staining.
            Wells with no data are shown in gray.
        """
    
    def _parse_well_position(self, well_name):
        """Convert well name (e.g., 'A01', 'B12') to row, col indices"""
        if len(well_name) < 2:
            return None, None
        
        row_letter = well_name[0].upper()
        try:
            col_num = int(well_name[1:])
            row_idx = ord(row_letter) - ord('A')
            col_idx = col_num - 1
            return row_idx, col_idx
        except ValueError:
            return None, None
    
    def plot(self, plate_format='96'):
        """Create plate heatmap visualization"""
        # Determine plate dimensions
        if plate_format == '96':
            rows, cols = 8, 12
        elif plate_format == '384':
            rows, cols = 16, 24
        else:
            rows, cols = 8, 12  # Default to 96-well
        
        # Initialize efficiency matrix
        efficiency_matrix = np.full((rows, cols), np.nan)
        
        # Fill matrix with efficiency data
        for _, row in self.df.iterrows():
            well = row['well']
            row_idx, col_idx = self._parse_well_position(well)
            if row_idx is not None and col_idx is not None and row_idx < rows and col_idx < cols:
                efficiency_matrix[row_idx, col_idx] = row['efficiency']
        
        # Create the plot
        fig, ax = plt.subplots(figsize=(12, 8))
        
        # Create heatmap
        im = ax.imshow(efficiency_matrix, cmap='RdYlBu_r', vmin=0, vmax=100, aspect='equal')
        
        # Add colorbar
        cbar = plt.colorbar(im, ax=ax, shrink=0.8)
        cbar.set_label('Differentiation Efficiency (%)', rotation=270, labelpad=20)
        
        # Set ticks and labels
        ax.set_xticks(range(cols))
        ax.set_yticks(range(rows))
        ax.set_xticklabels([f'{i+1:02d}' for i in range(cols)])
        ax.set_yticklabels([chr(ord('A') + i) for i in range(rows)])
        
        # Add well labels with efficiency values
        for i in range(rows):
            for j in range(cols):
                if not np.isnan(efficiency_matrix[i, j]):
                    text_color = 'white' if efficiency_matrix[i, j] < 50 else 'black'
                    ax.text(j, i, f'{efficiency_matrix[i, j]:.1f}%', 
                           ha='center', va='center', color=text_color, fontsize=8)
        
        ax.set_title(self.title, fontsize=14, pad=20)
        ax.set_xlabel('Column')
        ax.set_ylabel('Row')
        
        plt.tight_layout()
        if not os.path.exists(cache):
            os.makedirs(cache)
        plt.savefig(os.path.join(cache, self.fn), dpi=300, bbox_inches='tight')
        return fig


class DifferentiationBarCharts:
    """Bar charts for differentiation data visualization"""
    
    def __init__(self, df: pd.DataFrame, plate_name: str = None, day: int = None):
        self.df = df.copy()
        self.plate_name = plate_name or "Unknown"
        self.day = day or "Unknown"
        
        # Calculate efficiency percentage
        self.df['efficiency'] = (self.df['positive'] / self.df['total'] * 100).fillna(0)
        
        # Sort by well name for consistent ordering
        self.df = self.df.sort_values('well')
    
    def plot_efficiency_bars(self):
        """Create bar chart of differentiation efficiency per well"""
        fig, ax = plt.subplots(figsize=(15, 6))
        
        bars = ax.bar(self.df['well'], self.df['efficiency'], 
                     color='steelblue', alpha=0.7, edgecolor='black', linewidth=0.5)
        
        # Color bars based on efficiency level
        for i, (bar, eff) in enumerate(zip(bars, self.df['efficiency'])):
            if eff >= 75:
                bar.set_color('darkgreen')
            elif eff >= 50:
                bar.set_color('orange')
            elif eff >= 25:
                bar.set_color('gold')
            else:
                bar.set_color('lightcoral')
        
        ax.set_xlabel('Well')
        ax.set_ylabel('Differentiation Efficiency (%)')
        ax.set_title(f'Differentiation Efficiency by Well - {self.plate_name} Day {self.day}')
        ax.set_ylim(0, 100)
        
        # Rotate x-axis labels for better readability
        plt.xticks(rotation=45, ha='right')
        
        # Add efficiency values on top of bars
        for bar, eff in zip(bars, self.df['efficiency']):
            height = bar.get_height()
            ax.text(bar.get_x() + bar.get_width()/2., height + 1,
                   f'{eff:.1f}%', ha='center', va='bottom', fontsize=8)
        
        plt.tight_layout()
        fn = f"diff_efficiency_bars_{self.plate_name}_day{self.day}.png"
        if not os.path.exists(cache):
            os.makedirs(cache)
        plt.savefig(os.path.join(cache, fn), dpi=300, bbox_inches='tight')
        return fig
    
    def plot_cell_count_bars(self):
        """Create bar chart of total cell counts per well"""
        fig, ax = plt.subplots(figsize=(15, 6))
        
        bars = ax.bar(self.df['well'], self.df['total'], 
                     color='lightblue', alpha=0.7, edgecolor='black', linewidth=0.5)
        
        # Color bars based on cell count (quality indicator)
        median_count = self.df['total'].median()
        for bar, count in zip(bars, self.df['total']):
            if count < median_count * 0.5:  # Low cell count
                bar.set_color('lightcoral')
            elif count > median_count * 1.5:  # High cell count
                bar.set_color('darkgreen')
            else:
                bar.set_color('lightblue')
        
        ax.set_xlabel('Well')
        ax.set_ylabel('Total Cell Count')
        ax.set_title(f'Total Cell Count by Well - {self.plate_name} Day {self.day}')
        
        # Rotate x-axis labels
        plt.xticks(rotation=45, ha='right')
        
        # Add count values on top of bars
        for bar, count in zip(bars, self.df['total']):
            height = bar.get_height()
            ax.text(bar.get_x() + bar.get_width()/2., height + max(self.df['total']) * 0.01,
                   f'{int(count)}', ha='center', va='bottom', fontsize=8)
        
        plt.tight_layout()
        fn = f"diff_cellcount_bars_{self.plate_name}_day{self.day}.png"
        if not os.path.exists(cache):
            os.makedirs(cache)
        plt.savefig(os.path.join(cache, fn), dpi=300, bbox_inches='tight')
        return fig


class DifferentiationStackedBars:
    """Stacked bar charts showing positive/negative cell proportions"""
    
    def __init__(self, df: pd.DataFrame, plate_name: str = None, day: int = None):
        self.df = df.copy()
        self.plate_name = plate_name or "Unknown"
        self.day = day or "Unknown"
        
        # Sort by well name for consistent ordering
        self.df = self.df.sort_values('well')
    
    def plot(self):
        """Create stacked bar chart showing positive/negative proportions"""
        fig, ax = plt.subplots(figsize=(15, 8))
        
        wells = self.df['well']
        positive_counts = self.df['positive']
        negative_counts = self.df['negative']
        
        # Create stacked bars
        bars1 = ax.bar(wells, positive_counts, label='Differentiated (Positive)', 
                      color='darkgreen', alpha=0.8)
        bars2 = ax.bar(wells, negative_counts, bottom=positive_counts, 
                      label='Undifferentiated (Negative)', color='lightcoral', alpha=0.8)
        
        ax.set_xlabel('Well')
        ax.set_ylabel('Cell Count')
        ax.set_title(f'Cell Differentiation Status by Well - {self.plate_name} Day {self.day}')
        ax.legend()
        
        # Rotate x-axis labels
        plt.xticks(rotation=45, ha='right')
        
        # Add percentage labels on bars
        for i, (well, pos, neg, total) in enumerate(zip(wells, positive_counts, negative_counts, self.df['total'])):
            if total > 0:
                pos_pct = (pos / total) * 100
                # Add percentage label in the middle of the positive section
                if pos > 0:
                    ax.text(i, pos/2, f'{pos_pct:.1f}%', ha='center', va='center', 
                           color='white', fontweight='bold', fontsize=9)
                
                # Add total count at the top
                ax.text(i, total + max(self.df['total']) * 0.01, f'{int(total)}', 
                       ha='center', va='bottom', fontsize=8)
        
        plt.tight_layout()
        fn = f"diff_stacked_bars_{self.plate_name}_day{self.day}.png"
        if not os.path.exists(cache):
            os.makedirs(cache)
        plt.savefig(os.path.join(cache, fn), dpi=300, bbox_inches='tight')
        return fig


class DifferentiationTimeSeries:
    """Time series plots for longitudinal differentiation data"""

    def __init__(self, df_list: list, plate_name: str = None, days: list = None):
        """
        Initialize with list of DataFrames, one for each time point

        Args:
            df_list: List of DataFrames with differentiation data
            plate_name: Name of the plate
            days: List of day numbers corresponding to each DataFrame
        """
        self.df_list = df_list
        self.plate_name = plate_name or "Unknown"
        self.days = days or list(range(len(df_list)))

        # Combine all data with day information
        self.combined_df = []
        for day, df in zip(self.days, df_list):
            df_copy = df.copy()
            df_copy['day'] = day
            df_copy['efficiency'] = (df_copy['positive'] / df_copy['total'] * 100).fillna(0)
            self.combined_df.append(df_copy)

        self.combined_df = pd.concat(self.combined_df, ignore_index=True)

    def plot_efficiency_trends(self, selected_wells=None):
        """Plot efficiency trends over time for selected wells"""
        if selected_wells is None:
            # Select a representative subset of wells if none specified
            all_wells = self.combined_df['well'].unique()
            selected_wells = sorted(all_wells)[:12]  # First 12 wells

        fig, ax = plt.subplots(figsize=(12, 8))

        # Plot trend line for each selected well
        colors = plt.cm.tab20(np.linspace(0, 1, len(selected_wells)))

        for well, color in zip(selected_wells, colors):
            well_data = self.combined_df[self.combined_df['well'] == well]
            if len(well_data) > 1:  # Only plot if we have multiple time points
                ax.plot(well_data['day'], well_data['efficiency'],
                       marker='o', label=well, color=color, linewidth=2, markersize=6)

        ax.set_xlabel('Culture Day')
        ax.set_ylabel('Differentiation Efficiency (%)')
        ax.set_title(f'Differentiation Efficiency Trends - {self.plate_name}')
        ax.set_ylim(0, 100)
        ax.grid(True, alpha=0.3)
        ax.legend(bbox_to_anchor=(1.05, 1), loc='upper left')

        plt.tight_layout()
        fn = f"diff_timeseries_{self.plate_name}.png"
        if not os.path.exists(cache):
            os.makedirs(cache)
        plt.savefig(os.path.join(cache, fn), dpi=300, bbox_inches='tight')
        return fig

    def plot_average_trend(self):
        """Plot average differentiation efficiency across all wells over time"""
        fig, ax = plt.subplots(figsize=(10, 6))

        # Calculate statistics by day
        daily_stats = self.combined_df.groupby('day')['efficiency'].agg(['mean', 'std', 'count']).reset_index()

        # Plot mean with error bars
        ax.errorbar(daily_stats['day'], daily_stats['mean'],
                   yerr=daily_stats['std'], marker='o', linewidth=2,
                   markersize=8, capsize=5, capthick=2, color='steelblue')

        # Add data points for individual wells (with transparency)
        for well in self.combined_df['well'].unique():
            well_data = self.combined_df[self.combined_df['well'] == well]
            ax.plot(well_data['day'], well_data['efficiency'],
                   'o', alpha=0.3, color='gray', markersize=4)

        ax.set_xlabel('Culture Day')
        ax.set_ylabel('Differentiation Efficiency (%)')
        ax.set_title(f'Average Differentiation Efficiency Over Time - {self.plate_name}')
        ax.set_ylim(0, 100)
        ax.grid(True, alpha=0.3)

        # Add sample size annotations
        for _, row in daily_stats.iterrows():
            ax.annotate(f'n={int(row["count"])}',
                       (row['day'], row['mean'] + row['std'] + 5),
                       ha='center', va='bottom', fontsize=9)

        plt.tight_layout()
        fn = f"diff_average_trend_{self.plate_name}.png"
        if not os.path.exists(cache):
            os.makedirs(cache)
        plt.savefig(os.path.join(cache, fn), dpi=300, bbox_inches='tight')
        return fig
