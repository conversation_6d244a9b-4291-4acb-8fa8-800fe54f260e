import sys
import os
# Add the current directory's src to Python path FIRST
current_dir = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
src_path = os.path.join(current_dir, 'src')
if src_path not in sys.path:
    sys.path.insert(0, src_path)

from base.database import get_db
from plates.model import Plate
from images.pipeline import Pipeline, negatives
from images.match import ImageMatcher
from images.repo import Repo
from base.util import make_logger
from base.io import cache_store
import pandas as pd
import traceback
import numpy as np

from images.brightfield import BrightfieldAnalysis
from base.clients import bigquery
from qc.config import DevelopmentConfig

log = make_logger(__name__)

# BigQuery configuration
conf = DevelopmentConfig()
project_id = conf.PROJECT_ID
bq_dataset = conf.VERSION_2_DATASET
bq_lipid_table = conf.OLYMPUS_LIPID_TABLE  # Individual cell measurements
bq_agg_table = conf.OLYMPUS_CLUSTER_TABLE  # Aggregated measurements

def get_bigquery_measurements(bt_key, well_name, table_type="individual"):
    """
    Get measurements from BigQuery for Olympus data
    Args:
        bt_key: The bt_key for the image set (e.g., "Plate0210/P_0210_14_01")
        well_name: Well name (e.g., "C3")
        table_type: "individual" for cell measurements or "aggregated" for cluster stats
    Returns:
        DataFrame: BigQuery results
    """
    try:
        bq_client = bigquery()

        if table_type == "individual":
            table = f"{project_id}.{bq_dataset}.{bq_lipid_table}"
            # Query for individual cell measurements
            query = f"""
            SELECT * FROM `{table}`
            WHERE object_uri LIKE '%{bt_key}/{well_name}%'
            """
        else:
            table = f"{project_id}.{bq_dataset}.{bq_agg_table}"
            # Query for aggregated measurements
            query = f"""
            SELECT * FROM `{table}`
            WHERE object_uri LIKE '%{bt_key}/{well_name}%'
            """

        log.info(f"Executing BigQuery: {query}")
        query_job = bq_client.query(query)
        df = query_job.result().to_dataframe()
        log.info(f"Found {len(df)} records in BigQuery {table_type} table")
        return df

    except Exception as e:
        log.error(f"Error querying BigQuery: {e}")
        return pd.DataFrame()  # Return empty DataFrame on error


def check_database_connection():
    """Check if database is available"""
    try:
        db = get_db()
        # Try a simple query to test connection
        db.execute("SELECT 1")
        log.info("✅ Database connection successful")
        return True
    except Exception as e:
        log.error(f"❌ Database connection failed: {e}")
        log.info("To start the database, run: docker-compose up -d postgres")
        return False

def find_olympus_image_set(repo, plate_name, culture_day):
    """Find an Olympus image set in the repository"""
    log.info(f"Looking for Olympus image set: {plate_name}, day {culture_day}")

    # Try to find the image set
    ims = repo.image_set(plate_name, culture_day)
    if ims:
        log.info(f"Found image set: {ims}")
        return ims

    # If not found, list available image sets for debugging
    log.info("Available image sets:")
    for bt_key, image_set in repo.image_sets.bt_key_map.items():
        if hasattr(image_set, 'plate_name') and plate_name in str(image_set.plate_name):
            log.info(f"  {bt_key}: {image_set.plate_name} day {getattr(image_set, 'day', 'unknown')}")

    return None

def demo_olympus_repository_access(repo, bt_key, well_name):
    """
    Demonstrate accessing Olympus images through repository pattern
    This shows that the repository abstraction works without needing database connections
    """

    log.info(f"=== Repository Access Demo ===")
    log.info(f"Repository: {repo}")
    log.info(f"BT Key: {bt_key}")
    log.info(f"Well: {well_name}")

    try:
        # Get the image set
        ims = repo.image_sets.bt_key_map[bt_key]
        log.info(f"Image set: {ims}")

        # Get the well
        rwell = repo.get_well(bt_key, well_name)
        if not rwell:
            log.error(f"Well {well_name} not found")
            return None

        log.info(f"Well: {rwell}")
        log.info(f"Available channels: {rwell.channels()}")

        # Get image info
        default_image = rwell.default_image()
        log.info(f"Default image: {default_image}")
        log.info(f"Image info: {default_image.info}")

        # Try to load the image data (this demonstrates the repository pattern works)
        log.info("Loading image data...")
        image_data = default_image.data()
        log.info(f"Image loaded successfully: {image_data.shape} {image_data.dtype}")

        # Show some basic statistics
        log.info(f"Image statistics:")
        log.info(f"  Min: {image_data.min()}")
        log.info(f"  Max: {image_data.max()}")
        log.info(f"  Mean: {image_data.mean():.2f}")
        log.info(f"  Shape: {image_data.shape}")

        # Demonstrate that we can access pixel calibration info
        if hasattr(default_image.info, 'resx') and default_image.info.resx:
            pixel_size_um = default_image.info.resx
            pixels_per_mm = 1000.0 / pixel_size_um
            log.info(f"Pixel calibration:")
            log.info(f"  Pixel size: {pixel_size_um:.3f} μm")
            log.info(f"  Pixels per mm: {pixels_per_mm:.1f}")

        return image_data

    except Exception as e:
        log.error(f"Error accessing repository: {e}")
        traceback.print_exc()
        return None

def explore_available_data(repo):
    """Explore what data is available in the repository"""
    log.info(f"Exploring repository: {repo}")
    log.info(f"Repository details: {repo.name}, {repo.project}, {repo.bucket}")

    # Get some image sets to see what's available
    image_sets = repo.image_sets
    log.info(f"Total image sets: {len(image_sets.image_sets)}")

    # Show first few image sets
    count = 0
    for bt_key, ims in image_sets.bt_key_map.items():
        if count >= 5:  # Just show first 5
            break
        log.info(f"  {bt_key}: {getattr(ims, 'plate_name', 'unknown')} day {getattr(ims, 'day', 'unknown')}")
        if hasattr(ims, 'wells'):
            log.info(f"    Wells: {list(ims.wells.keys())[:5]}...")  # Show first 5 wells
        count += 1

    return list(image_sets.bt_key_map.keys())[:3]  # Return first 3 for testing

def find_olympus_image_set(repo, plate_name, culture_day):
    """Find a BF Olympus image set in the repository"""
    log.info(f"Looking for BF Olympus image set: {plate_name}, day {culture_day}")

    # Look specifically for BF image sets
    for bt_key, image_set in repo.image_sets.bt_key_map.items():
        if (hasattr(image_set, 'plate_name') and plate_name in str(image_set.plate_name) and
            hasattr(image_set, 'day') and image_set.day == culture_day and
            hasattr(image_set, 'lighting') and image_set.lighting == 'BF'):
            log.info(f"Found BF image set: {image_set}")
            return image_set

    # If not found, list available image sets for debugging
    log.info("Available image sets:")
    for bt_key, image_set in repo.image_sets.bt_key_map.items():
        if hasattr(image_set, 'plate_name') and plate_name in str(image_set.plate_name):
            log.info(f"  {bt_key}: {image_set.plate_name} day {getattr(image_set, 'day', 'unknown')} lighting: {getattr(image_set, 'lighting', 'unknown')}")

    return None

def process_olympus_image_with_repo(db, repo, repo_image_set, plate_name, culture_day, well_name):
    """Process an Olympus image using the repository pattern and BrightfieldAnalysis"""

    log.info(f"Processing with image set: {repo_image_set}")
    
    # Get the plate from database
    plate = db.query(Plate).filter(Plate.name == plate_name).first()
    if not plate:
        log.error(f"Plate {plate_name} not found in database")
        return None, None
    
    matcher = ImageMatcher(db, plate, [repo])
    
    # Try to find existing database image set
    db_image_set = None
    for ims in matcher.imss:
        if ims.bt_key == repo_image_set.bt_key:
            db_image_set = ims
            log.info(f"Found existing database image set: {db_image_set}")
            break
    
    if db_image_set is None:
        log.info("Inserting repository image set into database")
        db_image_set, status, msg = matcher.insert_image_set(repo_image_set)
        log.info(f"Insert result: {status} - {msg}")
    
    if db_image_set is None:
        log.error("Failed to create database image set")
        return None, None
    
    # Create a store for caching results
    store_path = f"olympus_brightfield_analysis/{plate_name}/day{culture_day}/{well_name}"
    store = cache_store() / store_path
    
    cts = negatives(db)
    pipeline = Pipeline(BrightfieldAnalysis, db_image_set, cts, store)
    
    # Create the analysis instance
    analyzer = BrightfieldAnalysis(pipeline, db_image_set, well_name)

    # CRITICAL FIX: Override the bt_key to use the repository image set's bt_key
    # The database image set might have a different bt_key than the repository image set
    log.info(f"Database image set bt_key: {db_image_set.bt_key}")
    log.info(f"Repository image set bt_key: {repo_image_set.bt_key}")
    analyzer.image_set.bt_key = repo_image_set.bt_key
    log.info(f"Using repository bt_key: {repo_image_set.bt_key}")

    log.info(f"Processing Olympus image for well {well_name}")

    try:
        # DEBUG: Check what images are available for this well
        rwell = repo.get_well(repo_image_set.bt_key, well_name)
        if rwell and rwell.images:
            log.info(f"DEBUG: Images available for well {well_name}:")
            for channel, image_file in rwell.images.items():
                log.info(f"  Channel: {channel}, File: {image_file.fn}")

            # Check specifically for BF images using the same logic as BrightfieldAnalysis
            if repo.name == "olympus":
                bf_images = [im for im in rwell.images.values() if im.channel.lower() in ["vsi-raw", "bf"]]
                log.info(f"DEBUG: Olympus BF images (vsi-raw or bf) found: {len(bf_images)}")
            else:
                bf_images = [im for im in rwell.images.values() if im.channel.lower() == "bf"]
                log.info(f"DEBUG: BF images found: {len(bf_images)}")

            for bf_img in bf_images:
                log.info(f"  BF image: {bf_img.fn}, channel: {bf_img.channel}")

            # Also check repo name
            log.info(f"DEBUG: Repository name: {repo.name}")
        else:
            log.error(f"DEBUG: No well or images found for {well_name}")

        # Load the image using the repository pattern
        image = analyzer.load_image
        log.info(f"Loaded image: {image.shape} {image.dtype}")
        
        # Create mock metadata (since Olympus doesn't have scan_metadata.json like Cytosmart)
        # We need to get pixel calibration from the image info
        rwell = repo.get_well(repo_image_set.bt_key, well_name)
        if not rwell:
            raise ValueError(f"Well {well_name} not found")
        
        # Get pixel calibration from image info
        default_image = rwell.default_image()
        pixel_calibration = default_image.info.resx  # micrometers per pixel
        pixels_per_mm = 1000.0 / pixel_calibration if pixel_calibration > 0 else 742  # fallback
        
        # Debug metadata values
        log.info(f"DEBUG: repo_image_set.id = {repo_image_set.id}")
        log.info(f"DEBUG: repo_image_set.experiment_id = {getattr(repo_image_set, 'experiment_id', 'None')}")

        metadata = {
            'pixelsPerMm': int(pixels_per_mm),
            'experimentId': str(getattr(repo_image_set, 'experiment_id', plate_name)),
            'scanNumber': 1,  # Use a default numeric value for Olympus
            'experimentName': f"{plate_name}_Day{culture_day}",
            'numberOfWells': 96  # Plate0210 is 96-well
        }
        
        # Segment the image
        mask = analyzer.segment(image)
        log.info(f"Segmentation complete: {mask.shape}, {len(np.unique(mask))} regions")
        
        # Create object URI for measurements using repository info
        objectURI = f"gs://{repo.bucket}/{repo_image_set.bt_key}/{well_name}/BF.tiff"
        
        # Measure the mask
        cp_df, agg_df = analyzer.measure(mask, metadata, objectURI)
        log.info("Measurement complete")
        
        return cp_df, agg_df
        
    except Exception as e:
        log.error(f"Error processing image: {e}")
        traceback.print_exc()
        return None, None

def compare_results(new_df, legacy_df, metrics=None):
    """
    Compare our processing results with legacy CSV data
    """
    if metrics is None:
        # Map between our column names and legacy column names
        metrics = {
            'um_area': 'area',  # Legacy uses um_area, we use area (after scaling)
            'pixel_area': 'area',  # For pixel-level comparison
            'perimeter': 'perimeter',
            'cluster_id': 'cluster'
        }
    
    if legacy_df.empty:
        log.info("No legacy data found for comparison")
        return None
    
    if new_df is None or new_df.empty:
        log.info("No new measurements generated")
        return None
    
    log.info("=== Comparison of Results ===")
    log.info(f"Legacy CSV records: {len(legacy_df)}")
    log.info(f"New measurements: {len(new_df)}")
    
    results = {
        'record_count_legacy': len(legacy_df),
        'record_count_new': len(new_df),
        'metrics': {}
    }
    
    for new_col, legacy_col in metrics.items():
        if legacy_col in legacy_df.columns and new_col in new_df.columns:
            legacy_mean = legacy_df[legacy_col].mean()
            new_mean = new_df[new_col].mean()
            diff_pct = ((new_mean - legacy_mean) / legacy_mean) * 100 if legacy_mean != 0 else float('inf')
            
            log.info(f"{new_col} vs {legacy_col}:")
            log.info(f"  Legacy mean: {legacy_mean:.2f}")
            log.info(f"  New mean: {new_mean:.2f}")
            log.info(f"  Difference: {diff_pct:.2f}%")
            
            results['metrics'][new_col] = {
                'legacy_mean': legacy_mean,
                'new_mean': new_mean,
                'diff_pct': diff_pct
            }
    
    return results

def explore_available_data(repo):
    """Explore what data is available in the repository"""
    log.info(f"Exploring repository: {repo}")
    log.info(f"Repository details: {repo.name}, {repo.project}, {repo.bucket}")

    # Get some image sets to see what's available
    image_sets = repo.image_sets
    log.info(f"Total image sets: {len(image_sets.image_sets)}")

    # Show first few image sets
    count = 0
    for bt_key, ims in image_sets.bt_key_map.items():
        if count >= 5:  # Just show first 5
            break
        log.info(f"  {bt_key}: {getattr(ims, 'plate_name', 'unknown')} day {getattr(ims, 'day', 'unknown')}")
        if hasattr(ims, 'wells'):
            log.info(f"    Wells: {list(ims.wells.keys())[:5]}...")  # Show first 5 wells
        count += 1

    return list(image_sets.bt_key_map.keys())[:3]  # Return first 3 for testing

def print_comparison_results(cp_df, agg_df, well_name, bt_key):
    """Print results in a format similar to CytoSmart comparison"""
    print("\n" + "="*120)
    print(f"COMPARISON FOR WELL: {well_name}")
    print("="*120)

    if len(cp_df) == 0:
        print("No cells detected")
        return

    # Get BigQuery data for comparison
    bq_cp_df = get_bigquery_measurements(bt_key, well_name, "individual")
    bq_agg_df = get_bigquery_measurements(bt_key, well_name, "aggregated")

    print("\nINDIVIDUAL CELL MEASUREMENTS:")
    print("="*120)
    print(f"{'Metric':<20} {'Olympus Processing':<25} {'BigQuery':<25} {'Diff %':<15}")
    print("-"*120)

    # Individual cell metrics
    for idx, row in cp_df.iterrows():
        print(f"Cell {idx+1}:")

        # Get corresponding BigQuery data if available
        bq_area = bq_cp_df.iloc[idx]['area'] if idx < len(bq_cp_df) else None
        bq_centroid_0 = bq_cp_df.iloc[idx]['centroid_0'] if idx < len(bq_cp_df) else None
        bq_centroid_1 = bq_cp_df.iloc[idx]['centroid_1'] if idx < len(bq_cp_df) else None
        bq_circularity = bq_cp_df.iloc[idx]['circularity'] if idx < len(bq_cp_df) else None
        bq_perimeter = bq_cp_df.iloc[idx]['perimeter'] if idx < len(bq_cp_df) else None

        # Calculate differences
        def calc_diff(new_val, old_val):
            if old_val is None or old_val == 0:
                return "N/A"
            return f"{((new_val - old_val) / old_val) * 100:.2f}"

        bq_area_str = f"{bq_area:.4f}" if bq_area is not None else "N/A"
        bq_centroid_0_str = f"{bq_centroid_0:.4f}" if bq_centroid_0 is not None else "N/A"
        bq_centroid_1_str = f"{bq_centroid_1:.4f}" if bq_centroid_1 is not None else "N/A"
        bq_circularity_str = f"{bq_circularity:.4f}" if bq_circularity is not None else "N/A"
        bq_perimeter_str = f"{bq_perimeter:.4f}" if bq_perimeter is not None else "N/A"

        print(f"{'  area':<18} {row['area']:<25.4f} {bq_area_str:<25} {calc_diff(row['area'], bq_area):<15}")
        print(f"{'  centroid_0':<18} {row['centroid_0']:<25.4f} {bq_centroid_0_str:<25} {calc_diff(row['centroid_0'], bq_centroid_0):<15}")
        print(f"{'  centroid_1':<18} {row['centroid_1']:<25.4f} {bq_centroid_1_str:<25} {calc_diff(row['centroid_1'], bq_centroid_1):<15}")
        print(f"{'  circularity':<18} {row['circularity']:<25.4f} {bq_circularity_str:<25} {calc_diff(row['circularity'], bq_circularity):<15}")
        print(f"{'  perimeter':<18} {row['perimeter']:<25.4f} {bq_perimeter_str:<25} {calc_diff(row['perimeter'], bq_perimeter):<15}")

    print("\nAGGREGATED MEASUREMENTS:")
    print("="*120)
    print(f"{'Metric':<25} {'Olympus Processing':<25} {'BigQuery':<25} {'Diff %':<15}")
    print("-"*120)

    if agg_df is not None and len(agg_df) > 0:
        agg_row = agg_df.iloc[0]
        bq_agg_row = bq_agg_df.iloc[0] if len(bq_agg_df) > 0 else None

        print(f"\nCluster 1:")

        # Helper function to get BigQuery value and calculate difference
        def get_bq_val_and_diff(metric_name, new_val):
            if bq_agg_row is not None and metric_name in bq_agg_row:
                bq_val = bq_agg_row[metric_name]
                bq_str = f"{bq_val:.4f}" if not pd.isna(bq_val) else "nan"
                diff = calc_diff(new_val, bq_val) if not pd.isna(bq_val) else "N/A"
                return bq_str, diff
            return "N/A", "N/A"

        # Print each metric with BigQuery comparison
        metrics = ['max_area', 'max_circ', 'mean_area', 'mean_circ', 'min_area', 'min_circ',
                  'q25_area', 'q50_area', 'q75_area', 'ratio_big_to_all_area']

        for metric in metrics:
            if metric in agg_row:
                val = agg_row[metric]
                val_str = f"{val:.4f}" if not pd.isna(val) else "nan"
                bq_str, diff_str = get_bq_val_and_diff(metric, val)
                print(f"{'  ' + metric:<23} {val_str:<25} {bq_str:<25} {diff_str:<15}")

        # Handle special NaN-prone metrics
        for metric in ['ratio_two_biggest_area', 'std_area', 'std_circ']:
            if metric in agg_row:
                val = agg_row[metric]
                val_str = "nan" if pd.isna(val) else f"{val:.4f}"
                bq_str, diff_str = get_bq_val_and_diff(metric, val)
                print(f"{'  ' + metric:<23} {val_str:<25} {bq_str:<25} {diff_str:<15}")

    print("\n")


def main():
    """Main function to test Olympus BrightfieldAnalysis pipeline"""

    log.info("Testing Olympus BrightfieldAnalysis pipeline")

    # Hardcoded test parameters
    plate_name = "Plate0210"
    culture_day = 14  # Try day 14 for more mature cell growth

    # Try multiple wells to find one with more cells
    candidate_wells = ["D2", "C3", "E4", "F5", "D5", "C4", "E3", "B3"]

    # Get database connection
    db = get_db()

    # Get Olympus repository
    repo = Repo("olympus")
    log.info(f"Connected to repository: {repo}")

    # Find a BF (brightfield) image set specifically
    ims = None
    log.info("Looking for BF image sets for Plate0210:")
    for bt_key, image_set in repo.image_sets.bt_key_map.items():
        if (hasattr(image_set, 'plate_name') and "0210" in str(image_set.plate_name) and
            hasattr(image_set, 'day') and image_set.day == culture_day):
            log.info(f"  {bt_key}: {image_set.plate_name} day {image_set.day} lighting: {getattr(image_set, 'lighting', 'unknown')}")
            # Look for BF (brightfield) image set
            if hasattr(image_set, 'lighting') and image_set.lighting == 'BF':
                ims = image_set
                log.info(f"✅ Found BF image set: {ims}")
                break

    if not ims:
        log.error(f"No BF image set found for {plate_name}, day {culture_day}")
        return

    log.info(f"Found image set: {ims}")

    # Check if well exists in the BF image set and show all available wells
    if hasattr(ims, 'wells'):
        available_wells = list(ims.wells.keys())
        log.info(f"Available wells in BF image set: {available_wells}")

        # Try multiple wells to find one with good cell count
        best_well = None
        best_cell_count = 0
        best_results = None

        for test_well in candidate_wells:
            if test_well not in ims.wells:
                log.info(f"Well {test_well} not available, skipping")
                continue

            log.info(f"Trying well {test_well}...")

            try:
                cp_df, agg_df = process_olympus_image_with_repo(db, repo, ims, plate_name, culture_day, test_well)

                if cp_df is not None:
                    cell_count = len(cp_df)
                    log.info(f"Well {test_well}: {cell_count} cells detected")

                    if cell_count > best_cell_count:
                        best_cell_count = cell_count
                        best_well = test_well
                        best_results = (cp_df, agg_df)

                    # If we found a good number of cells, use this well
                    if cell_count >= 5:
                        break
                else:
                    log.info(f"Well {test_well}: processing failed")

            except Exception as e:
                log.error(f"Error processing well {test_well}: {e}")
                continue

        if best_results is None:
            log.error("❌ No wells could be processed successfully")
            return

        cp_df, agg_df = best_results
        well_name = best_well
        log.info(f"Selected best well: {well_name} with {best_cell_count} cells")
    else:
        log.error("No wells found in BF image set")
        return

    if cp_df is not None:
        log.info("=== BrightfieldAnalysis Results ===")
        log.info(f"Individual cell measurements: {len(cp_df)} records")
        log.info(f"Aggregated measurements: {len(agg_df) if agg_df is not None else 0} records")

        # Show sample of results
        if len(cp_df) > 0:
            log.info("Sample individual measurements:")
            log.info(cp_df[['label', 'area', 'perimeter', 'circularity', 'cluster']].head())

        if agg_df is not None and len(agg_df) > 0:
            log.info("Sample aggregated measurements:")
            log.info(agg_df[['cluster', 'count_area', 'mean_area', 'mean_circ']].head())

        # Save results
        output_prefix = f"olympus_demo_{plate_name}_day{culture_day}_{well_name}"
        cp_df.to_csv(f"{output_prefix}_individual.csv", index=False)
        if agg_df is not None:
            agg_df.to_csv(f"{output_prefix}_aggregated.csv", index=False)

        log.info(f"Results saved with prefix: {output_prefix}")

        # Format results like CytoSmart comparison
        print_comparison_results(cp_df, agg_df, well_name, ims.bt_key)

        log.info("✅ BrightfieldAnalysis pipeline test completed successfully!")
        log.info("This demonstrates that the consolidated pipeline works with Olympus data!")
    else:
        log.warning("❌ Processing failed")

if __name__ == "__main__":
    main()
