#!/usr/bin/env python3
"""
Demo script for differentiation efficiency visualizations

This script generates sample differentiation data and creates all the visualization types
recommended for viewing differentiation efficiency results.
"""

import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import sys
import os

# Add src to path to import our modules
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..', 'src'))

from qc.differentiation_plots import (
    DifferentiationPlateHeatmap,
    DifferentiationBarCharts,
    DifferentiationStackedBars,
    DifferentiationTimeSeries
)


def generate_sample_data(plate_name="Plate001", day=7, num_wells=96):
    """Generate realistic sample differentiation data"""
    
    # Generate well names for a 96-well plate
    rows = ['A', 'B', 'C', 'D', 'E', 'F', 'G', 'H']
    cols = list(range(1, 13))  # 1-12
    
    wells = []
    for row in rows:
        for col in cols:
            wells.append(f"{row}{col:02d}")
    
    # Limit to requested number of wells
    wells = wells[:num_wells]
    
    # Generate realistic cell counts and differentiation rates
    np.random.seed(42)  # For reproducible results
    
    data = []
    for well in wells:
        # Simulate realistic cell counts (50-500 cells per well)
        total_cells = np.random.randint(50, 500)
        
        # Simulate differentiation efficiency with some spatial patterns
        # Wells closer to edges might have different efficiency
        row_idx = ord(well[0]) - ord('A')
        col_idx = int(well[1:]) - 1
        
        # Base efficiency with some variation
        base_efficiency = 0.4 + 0.3 * np.random.random()
        
        # Add spatial effects (edge effects)
        if row_idx == 0 or row_idx == 7 or col_idx == 0 or col_idx == 11:
            base_efficiency *= 0.8  # Edge wells have lower efficiency
        
        # Add some treatment effects (simulate different conditions)
        if col_idx < 4:  # First 4 columns - control
            base_efficiency *= 0.7
        elif col_idx < 8:  # Middle 4 columns - treatment 1
            base_efficiency *= 1.2
        else:  # Last 4 columns - treatment 2
            base_efficiency *= 1.1
        
        # Ensure efficiency is between 0 and 1
        base_efficiency = max(0, min(1, base_efficiency))
        
        # Calculate positive and negative cells
        positive_cells = int(total_cells * base_efficiency)
        negative_cells = total_cells - positive_cells
        
        data.append({
            'well': well,
            'positive': positive_cells,
            'negative': negative_cells,
            'total': total_cells
        })
    
    return pd.DataFrame(data)


def generate_time_series_data(plate_name="Plate001", days=[3, 7, 14, 21]):
    """Generate sample time series data for multiple days"""
    
    time_series_data = []
    
    for day in days:
        # Generate data for this day
        df = generate_sample_data(plate_name, day, num_wells=48)  # Smaller subset for demo
        
        # Simulate progression over time - efficiency generally increases
        day_factor = 1 + (day - 3) * 0.1  # Gradual increase over time
        
        for idx, row in df.iterrows():
            # Adjust efficiency based on day
            current_efficiency = row['positive'] / row['total']
            new_efficiency = min(0.9, current_efficiency * day_factor)
            
            new_positive = int(row['total'] * new_efficiency)
            new_negative = row['total'] - new_positive
            
            df.at[idx, 'positive'] = new_positive
            df.at[idx, 'negative'] = new_negative
        
        time_series_data.append(df)
    
    return time_series_data, days


def create_sample_visualizations():
    """Create all sample visualizations"""
    
    print("Generating sample differentiation efficiency visualizations...")
    
    # 1. Generate sample data for a single time point
    print("1. Creating plate heatmap...")
    df_single = generate_sample_data("Plate001", day=14, num_wells=96)
    
    # Create plate heatmap
    heatmap = DifferentiationPlateHeatmap(df_single, "Plate001", 14)
    fig1 = heatmap.plot()
    plt.show()
    print(f"   Saved: {heatmap.fn}")
    
    # 2. Create bar charts
    print("2. Creating bar charts...")
    bar_charts = DifferentiationBarCharts(df_single, "Plate001", 14)
    
    # Efficiency bar chart
    fig2 = bar_charts.plot_efficiency_bars()
    plt.show()
    print("   Saved: efficiency bar chart")
    
    # Cell count bar chart
    fig3 = bar_charts.plot_cell_count_bars()
    plt.show()
    print("   Saved: cell count bar chart")
    
    # 3. Create stacked bar chart
    print("3. Creating stacked bar chart...")
    stacked_bars = DifferentiationStackedBars(df_single, "Plate001", 14)
    fig4 = stacked_bars.plot()
    plt.show()
    print("   Saved: stacked bar chart")
    
    # 4. Create time series plots
    print("4. Creating time series plots...")
    time_series_data, days = generate_time_series_data("Plate001", [3, 7, 14, 21])
    
    time_series = DifferentiationTimeSeries(time_series_data, "Plate001", days)
    
    # Individual well trends
    selected_wells = ['A01', 'A02', 'B01', 'B02', 'C01', 'C02']  # Sample wells
    fig5 = time_series.plot_efficiency_trends(selected_wells)
    plt.show()
    print("   Saved: individual well trends")
    
    # Average trend
    fig6 = time_series.plot_average_trend()
    plt.show()
    print("   Saved: average trend")
    
    print("\nAll visualizations created successfully!")
    
    # Print sample data summary
    print("\nSample data summary:")
    print(f"Total wells: {len(df_single)}")
    print(f"Average efficiency: {(df_single['positive'] / df_single['total'] * 100).mean():.1f}%")
    print(f"Efficiency range: {(df_single['positive'] / df_single['total'] * 100).min():.1f}% - {(df_single['positive'] / df_single['total'] * 100).max():.1f}%")
    print(f"Average cell count: {df_single['total'].mean():.0f}")
    print(f"Cell count range: {df_single['total'].min()} - {df_single['total'].max()}")


def create_sample_csv():
    """Create a sample CSV file matching the differentiation pipeline output format"""
    
    print("\nCreating sample CSV file...")
    df = generate_sample_data("Plate001", day=14, num_wells=96)
    
    # Add efficiency column for reference
    df['efficiency_percent'] = (df['positive'] / df['total'] * 100).round(2)
    
    # Save to CSV
    output_file = "sample_differentiation_data.csv"
    df.to_csv(output_file, index=False)
    
    print(f"Sample CSV saved as: {output_file}")
    print("\nFirst 10 rows of sample data:")
    print(df.head(10).to_string(index=False))
    
    return df


if __name__ == "__main__":
    # Create sample CSV data
    sample_df = create_sample_csv()
    
    # Create all visualizations
    create_sample_visualizations()
    
    print("\n" + "="*60)
    print("VISUALIZATION GUIDE")
    print("="*60)
    print("""
1. PLATE HEATMAP: Shows spatial distribution of differentiation efficiency
   - Use to identify spatial patterns, edge effects, or treatment zones
   - Color scale: Red (low) to Blue (high) efficiency
   
2. EFFICIENCY BAR CHART: Direct comparison of efficiency across wells
   - Color coding: Green (>75%), Orange (50-75%), Yellow (25-50%), Red (<25%)
   - Use to identify best/worst performing wells
   
3. CELL COUNT BAR CHART: Quality control for cell seeding
   - Color coding based on deviation from median count
   - Use to identify wells with seeding problems
   
4. STACKED BAR CHART: Shows absolute numbers and proportions
   - Green = differentiated cells, Red = undifferentiated cells
   - Percentage labels show efficiency, numbers show total count
   
5. TIME SERIES PLOTS: Track differentiation progress over time
   - Individual well trends: Monitor specific conditions
   - Average trends: Overall experiment progression with error bars
   
USAGE TIPS:
- Combine heatmap + bar charts for comprehensive single-timepoint analysis
- Use time series for longitudinal studies
- Always check cell counts alongside efficiency for quality control
""")
