#!/usr/bin/env python3
"""
<PERSON><PERSON><PERSON> to create visualizations from actual differentiation pipeline results

This script reads CSV files generated by the differentiation pipeline and creates
comprehensive visualizations for analysis.

Usage:
    python visualize_differentiation_results.py <csv_file> [--plate-name PLATE] [--day DAY]
    
    For time series:
    python visualize_differentiation_results.py --time-series <csv1> <csv2> ... [--days DAY1 DAY2 ...]
"""

import pandas as pd
import matplotlib.pyplot as plt
import argparse
import sys
import os
from pathlib import Path

# Add src to path to import our modules
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..', 'src'))

from qc.differentiation_plots import (
    DifferentiationPlateHeatmap,
    DifferentiationBarCharts,
    DifferentiationStackedBars,
    DifferentiationTimeSeries
)


def load_differentiation_csv(csv_path):
    """Load and validate differentiation CSV file"""
    
    try:
        df = pd.read_csv(csv_path)
    except Exception as e:
        print(f"Error reading CSV file {csv_path}: {e}")
        return None
    
    # Validate required columns
    required_columns = ['well', 'positive', 'negative', 'total']
    missing_columns = [col for col in required_columns if col not in df.columns]
    
    if missing_columns:
        print(f"Error: Missing required columns in {csv_path}: {missing_columns}")
        print(f"Available columns: {list(df.columns)}")
        return None
    
    # Validate data types and values
    try:
        df['positive'] = pd.to_numeric(df['positive'])
        df['negative'] = pd.to_numeric(df['negative'])
        df['total'] = pd.to_numeric(df['total'])
    except Exception as e:
        print(f"Error: Invalid numeric data in {csv_path}: {e}")
        return None
    
    # Check data consistency
    calculated_total = df['positive'] + df['negative']
    inconsistent = df[abs(df['total'] - calculated_total) > 0.1]
    
    if len(inconsistent) > 0:
        print(f"Warning: Inconsistent totals found in {len(inconsistent)} rows")
        print("First few inconsistent rows:")
        print(inconsistent[['well', 'positive', 'negative', 'total']].head())
    
    print(f"Loaded {len(df)} wells from {csv_path}")
    return df


def extract_plate_info(csv_path):
    """Extract plate name and day from CSV filename"""
    
    filename = Path(csv_path).stem
    
    # Try to extract plate name and day from filename
    # Expected format: PlateXXX_dayYY_differentiation.csv
    parts = filename.split('_')
    
    plate_name = "Unknown"
    day = "Unknown"
    
    for part in parts:
        if part.startswith('Plate') or part.startswith('PD'):
            plate_name = part
        elif part.startswith('day') and len(part) > 3:
            try:
                day = int(part[3:])
            except ValueError:
                pass
    
    return plate_name, day


def create_single_timepoint_visualizations(df, plate_name, day, output_dir=None):
    """Create all visualizations for a single timepoint"""
    
    if output_dir:
        os.makedirs(output_dir, exist_ok=True)
    
    print(f"\nCreating visualizations for {plate_name} Day {day}")
    print(f"Data summary: {len(df)} wells, avg efficiency: {(df['positive']/df['total']*100).mean():.1f}%")
    
    # 1. Plate heatmap
    print("  Creating plate heatmap...")
    heatmap = DifferentiationPlateHeatmap(df, plate_name, day)
    fig1 = heatmap.plot()
    if output_dir:
        plt.savefig(os.path.join(output_dir, heatmap.fn), dpi=300, bbox_inches='tight')
    plt.show()
    
    # 2. Bar charts
    print("  Creating bar charts...")
    bar_charts = DifferentiationBarCharts(df, plate_name, day)
    
    # Efficiency bars
    fig2 = bar_charts.plot_efficiency_bars()
    if output_dir:
        plt.savefig(os.path.join(output_dir, f"diff_efficiency_bars_{plate_name}_day{day}.png"), 
                   dpi=300, bbox_inches='tight')
    plt.show()
    
    # Cell count bars
    fig3 = bar_charts.plot_cell_count_bars()
    if output_dir:
        plt.savefig(os.path.join(output_dir, f"diff_cellcount_bars_{plate_name}_day{day}.png"), 
                   dpi=300, bbox_inches='tight')
    plt.show()
    
    # 3. Stacked bars
    print("  Creating stacked bar chart...")
    stacked_bars = DifferentiationStackedBars(df, plate_name, day)
    fig4 = stacked_bars.plot()
    if output_dir:
        plt.savefig(os.path.join(output_dir, f"diff_stacked_bars_{plate_name}_day{day}.png"), 
                   dpi=300, bbox_inches='tight')
    plt.show()
    
    print("  All visualizations created!")


def create_time_series_visualizations(df_list, plate_name, days, output_dir=None):
    """Create time series visualizations"""
    
    if output_dir:
        os.makedirs(output_dir, exist_ok=True)
    
    print(f"\nCreating time series visualizations for {plate_name}")
    print(f"Time points: {days}")
    
    time_series = DifferentiationTimeSeries(df_list, plate_name, days)
    
    # Individual well trends (select representative wells)
    all_wells = set()
    for df in df_list:
        all_wells.update(df['well'].unique())
    
    # Select wells that appear in all timepoints
    common_wells = all_wells
    for df in df_list:
        common_wells = common_wells.intersection(set(df['well'].unique()))
    
    selected_wells = sorted(list(common_wells))[:12]  # First 12 common wells
    
    print(f"  Plotting trends for {len(selected_wells)} wells...")
    fig1 = time_series.plot_efficiency_trends(selected_wells)
    if output_dir:
        plt.savefig(os.path.join(output_dir, f"diff_timeseries_{plate_name}.png"), 
                   dpi=300, bbox_inches='tight')
    plt.show()
    
    # Average trend
    print("  Creating average trend plot...")
    fig2 = time_series.plot_average_trend()
    if output_dir:
        plt.savefig(os.path.join(output_dir, f"diff_average_trend_{plate_name}.png"), 
                   dpi=300, bbox_inches='tight')
    plt.show()
    
    print("  Time series visualizations created!")


def main():
    parser = argparse.ArgumentParser(description='Create differentiation efficiency visualizations')
    parser.add_argument('csv_files', nargs='+', help='CSV file(s) with differentiation data')
    parser.add_argument('--plate-name', help='Override plate name')
    parser.add_argument('--day', type=int, help='Override day number')
    parser.add_argument('--days', nargs='+', type=int, help='Day numbers for time series')
    parser.add_argument('--time-series', action='store_true', 
                       help='Create time series plots from multiple CSV files')
    parser.add_argument('--output-dir', help='Directory to save plots')
    
    args = parser.parse_args()
    
    if args.time_series:
        # Time series mode
        if len(args.csv_files) < 2:
            print("Error: Time series mode requires at least 2 CSV files")
            return
        
        # Load all CSV files
        df_list = []
        days = args.days or list(range(len(args.csv_files)))
        
        if len(days) != len(args.csv_files):
            print("Error: Number of days must match number of CSV files")
            return
        
        plate_name = args.plate_name
        
        for csv_file, day in zip(args.csv_files, days):
            df = load_differentiation_csv(csv_file)
            if df is None:
                return
            
            df_list.append(df)
            
            # Extract plate name from first file if not provided
            if plate_name is None:
                plate_name, _ = extract_plate_info(csv_file)
        
        create_time_series_visualizations(df_list, plate_name, days, args.output_dir)
        
    else:
        # Single timepoint mode
        if len(args.csv_files) != 1:
            print("Error: Single timepoint mode requires exactly 1 CSV file")
            return
        
        csv_file = args.csv_files[0]
        df = load_differentiation_csv(csv_file)
        if df is None:
            return
        
        # Get plate info
        plate_name = args.plate_name
        day = args.day
        
        if plate_name is None or day is None:
            extracted_plate, extracted_day = extract_plate_info(csv_file)
            plate_name = plate_name or extracted_plate
            day = day or extracted_day
        
        create_single_timepoint_visualizations(df, plate_name, day, args.output_dir)


if __name__ == "__main__":
    main()
